# Server Configuration
PORT=3000
NODE_ENV=development

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017
MONGODB_DB_NAME=shopify_automation

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h

# Return Prime Configuration
RETURN_PRIME_API_URL=https://api.returnprime.com/v1
RETURN_PRIME_ADMIN_ACCESS_TOKEN=de5fe93b536d04a451edd984d305577e02f3c424a40f8e8c2293a6bc4de229b4
RETURN_PRIME_WEBHOOK_SECRET=your_webhook_secret_here

# Shopify Configuration
SHOPIFY_STORE_URL=bakebuds.myshopify.com
SHOPIFY_ACCESS_TOKEN=your_shopify_access_token
SHOPIFY_API_VERSION=2023-10

# Email Configuration (Nodemailer)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# Google Sheets Configuration
GOOGLE_SHEETS_ID=your_google_sheets_id
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----"

# Airtable Configuration (Alternative to Google Sheets)
AIRTABLE_API_KEY=your_airtable_api_key
AIRTABLE_BASE_ID=your_airtable_base_id
AIRTABLE_TABLE_NAME=Returns

# In-memory idempotency (no Redis needed)

# Supplier Email Addresses
SMOKEDROP_EMAIL=<EMAIL>
BUDDIFY_EMAIL=<EMAIL>
CANNA_RIVER_EMAIL=<EMAIL>
DISCREET_SMOKER_EMAIL=<EMAIL>

# Internal Configuration
INTERNAL_REVIEW_EMAIL=<EMAIL>
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY_MS=5000
