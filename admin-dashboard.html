<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supplier Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 600;
        }

        .header p {
            opacity: 0.9;
            margin-top: 0.5rem;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }

        .stat-card h3 {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .stat-card p {
            color: #666;
            font-weight: 500;
        }

        .actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #4CAF50;
            color: white;
        }

        .btn-success:hover {
            background: #45a049;
        }

        .btn-danger {
            background: #f44336;
            color: white;
        }

        .btn-danger:hover {
            background: #da190b;
        }

        .search-box {
            width: 100%;
            max-width: 400px;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }

        .suppliers-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .table-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e1e5e9;
        }

        .table-header h2 {
            color: #333;
            font-size: 1.25rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1rem 1.5rem;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .priority-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .priority-high {
            background: #ffebee;
            color: #c62828;
        }

        .priority-medium {
            background: #fff3e0;
            color: #ef6c00;
        }

        .priority-low {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .status-verified {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-unverified {
            background: #fff3e0;
            color: #ef6c00;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-header h2 {
            color: #333;
        }

        .close {
            font-size: 2rem;
            cursor: pointer;
            color: #999;
        }

        .close:hover {
            color: #333;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .error {
            background: #ffebee;
            color: #c62828;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            table {
                font-size: 0.875rem;
            }

            th, td {
                padding: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏪 Supplier Admin Dashboard</h1>
        <p>Manage suppliers and their email configurations for webhook processing</p>
    </div>

    <div class="container">
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3 id="total-suppliers">-</h3>
                <p>Total Suppliers</p>
            </div>
            <div class="stat-card">
                <h3 id="active-suppliers">-</h3>
                <p>Active Suppliers</p>
            </div>
            <div class="stat-card">
                <h3 id="verified-suppliers">-</h3>
                <p>Verified Suppliers</p>
            </div>
            <div class="stat-card">
                <h3 id="high-priority">-</h3>
                <p>High Priority</p>
            </div>
        </div>

        <!-- Actions -->
        <div class="actions">
            <button class="btn btn-primary" onclick="openAddModal()">
                ➕ Add New Supplier
            </button>
            <button class="btn btn-success" onclick="refreshData()">
                🔄 Refresh Data
            </button>
            <button class="btn" onclick="testApiConnection()" style="background: #17a2b8; color: white;">
                🧪 Test API
            </button>
            <input type="text" class="search-box" id="search-input" placeholder="Search suppliers..." onkeyup="searchSuppliers()">
        </div>

        <!-- Suppliers Table -->
        <div class="suppliers-table">
            <div class="table-header">
                <h2>Suppliers</h2>
            </div>
            <div id="table-container">
                <div class="loading">Loading suppliers...</div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Supplier Modal -->
    <div id="supplier-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Add New Supplier</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <form id="supplier-form">
                <div class="form-group">
                    <label for="name">Supplier Name *</label>
                    <input type="text" id="name" name="name" required>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="priority">Priority</label>
                        <select id="priority" name="priority">
                            <option value="MEDIUM">Medium</option>
                            <option value="HIGH">High</option>
                            <option value="LOW">Low</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="verified">Verified</label>
                        <select id="verified" name="verified">
                            <option value="false">No</option>
                            <option value="true">Yes</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">Contact Email *</label>
                    <input type="email" id="email" name="email" required>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">Phone</label>
                        <input type="text" id="phone" name="phone">
                    </div>
                    <div class="form-group">
                        <label for="website">Website</label>
                        <input type="url" id="website" name="website">
                    </div>
                </div>

                <div class="form-group">
                    <label for="vendor-names">Shopify Vendor Names (comma-separated)</label>
                    <input type="text" id="vendor-names" name="vendorNames" placeholder="e.g., SmokeDrop, Smoke Drop">
                </div>

                <div class="form-group">
                    <label for="tags">Tags (comma-separated)</label>
                    <input type="text" id="tags" name="tags" placeholder="e.g., vape, accessories, premium">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="processing-time">Processing Time</label>
                        <input type="text" id="processing-time" name="processingTime" value="2-3 business days">
                    </div>
                    <div class="form-group">
                        <label for="requires-approval">Requires Approval</label>
                        <select id="requires-approval" name="requiresApproval">
                            <option value="true">Yes</option>
                            <option value="false">No</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="notes">Notes</label>
                    <textarea id="notes" name="notes" rows="3"></textarea>
                </div>

                <div class="actions">
                    <button type="submit" class="btn btn-primary">Save Supplier</button>
                    <button type="button" class="btn" onclick="closeModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let suppliers = [];
        let editingSupplier = null;

        // API Base URL
        const API_BASE = '/admin';

        // Test server connection
        async function testConnection() {
            try {
                console.log('Testing server connection...');
                const response = await fetch('/health');

                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ Server is running:', data);

                    // Show MongoDB status
                    if (data.database && data.database.status === 'unhealthy') {
                        showError('⚠️ MongoDB is not connected. Using JSON fallback for supplier data.');
                    }

                    return true;
                } else {
                    console.log('❌ Server responded with error:', response.status);
                    return false;
                }
            } catch (error) {
                console.log('❌ Cannot connect to server:', error.message);
                showError('Cannot connect to server. Please ensure the server is running on port 3000.');
                return false;
            }
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 Initializing dashboard...');
            console.log('Current URL:', window.location.href);
            console.log('API Base:', API_BASE);

            // Test connection first
            const isConnected = await testConnection();

            if (isConnected) {
                console.log('✅ Server connection successful, loading data...');

                try {
                    // Load data with individual error handling
                    console.log('📊 Loading statistics...');
                    await loadStats();

                    console.log('👥 Loading suppliers...');
                    await loadSuppliers();

                    console.log('✅ Dashboard initialization complete');
                } catch (error) {
                    console.error('❌ Error during data loading:', error);
                    showError('Error loading dashboard data: ' + error.message);
                }
            } else {
                // Show connection error in the table
                const container = document.getElementById('table-container');
                container.innerHTML = `
                    <div class="loading" style="color: #f44336; text-align: center; padding: 2rem;">
                        <h3>❌ Server Connection Failed</h3>
                        <p>Cannot connect to the backend server.</p>
                        <p><strong>Please ensure:</strong></p>
                        <ul style="text-align: left; display: inline-block;">
                            <li>The server is running: <code>npm run dev</code></li>
                            <li>MongoDB is connected (optional - JSON fallback available)</li>
                            <li>Server is accessible on port 3000</li>
                        </ul>
                        <button onclick="location.reload()" style="margin-top: 15px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            Retry Connection
                        </button>
                    </div>
                `;
            }
        });

        // Load suppliers from API
        async function loadSuppliers() {
            try {
                console.log('Loading suppliers from:', `${API_BASE}/suppliers`);
                const response = await fetch(`${API_BASE}/suppliers`);

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Response data:', data);

                if (data.success) {
                    suppliers = data.data || [];
                    console.log('Loaded suppliers:', suppliers.length);
                    renderSuppliersTable();
                } else {
                    const errorMsg = data.error || 'Unknown error occurred';
                    console.error('API error:', errorMsg);
                    showError('Failed to load suppliers: ' + errorMsg);

                    // Show specific error for database issues
                    if (data.code === 'DATABASE_UNAVAILABLE') {
                        showError('Database not available. Please ensure MongoDB is connected and try again.');
                    }
                }
            } catch (error) {
                console.error('Load suppliers error:', error);
                showError('Failed to connect to server: ' + error.message);

                // Show fallback message
                const container = document.getElementById('table-container');
                container.innerHTML = `
                    <div class="loading" style="color: #f44336;">
                        ❌ Cannot connect to server<br>
                        <small>Please check if the server is running on port 3000</small><br>
                        <button onclick="loadSuppliers()" style="margin-top: 10px; padding: 5px 10px;">Retry</button>
                    </div>
                `;
            }
        }

        // Load statistics
        async function loadStats() {
            try {
                console.log('Loading stats from:', `${API_BASE}/stats/suppliers`);
                const response = await fetch(`${API_BASE}/stats/suppliers`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Stats data:', data);

                if (data.success) {
                    const stats = data.data;
                    document.getElementById('total-suppliers').textContent = stats.total || '0';
                    document.getElementById('active-suppliers').textContent = stats.active || '0';
                    document.getElementById('verified-suppliers').textContent = stats.verified || '0';
                    document.getElementById('high-priority').textContent = stats.byPriority?.HIGH || '0';
                } else {
                    console.error('Stats API error:', data.error);
                    // Set default values on error
                    document.getElementById('total-suppliers').textContent = '?';
                    document.getElementById('active-suppliers').textContent = '?';
                    document.getElementById('verified-suppliers').textContent = '?';
                    document.getElementById('high-priority').textContent = '?';
                }
            } catch (error) {
                console.error('Failed to load stats:', error);
                // Set error indicators
                document.getElementById('total-suppliers').textContent = '!';
                document.getElementById('active-suppliers').textContent = '!';
                document.getElementById('verified-suppliers').textContent = '!';
                document.getElementById('high-priority').textContent = '!';
            }
        }

        // Render suppliers table
        function renderSuppliersTable() {
            const container = document.getElementById('table-container');
            
            if (suppliers.length === 0) {
                container.innerHTML = '<div class="loading">No suppliers found</div>';
                return;
            }

            const table = `
                <table>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Vendor Names</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${suppliers.map(supplier => `
                            <tr>
                                <td><strong>${supplier.name}</strong></td>
                                <td>${supplier.contact.email}</td>
                                <td><span class="priority-badge priority-${supplier.priority.toLowerCase()}">${supplier.priority}</span></td>
                                <td><span class="status-badge status-${supplier.verified ? 'verified' : 'unverified'}">${supplier.verified ? 'Verified' : 'Unverified'}</span></td>
                                <td>${supplier.shopifyVendorNames ? supplier.shopifyVendorNames.join(', ') : '-'}</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editSupplier('${supplier._id}')" style="padding: 0.5rem; margin-right: 0.5rem;">Edit</button>
                                    <button class="btn btn-danger" onclick="deleteSupplier('${supplier._id}')" style="padding: 0.5rem;">Delete</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = table;
        }

        // Debounced search function
        let searchTimeout;
        function searchSuppliers() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const searchTerm = document.getElementById('search-input').value.toLowerCase();

                if (searchTerm.length === 0) {
                    // Show all suppliers if search is empty
                    renderSuppliersTable();
                    return;
                }

                if (searchTerm.length < 2) {
                    // Don't search for single characters
                    return;
                }

                const filteredSuppliers = suppliers.filter(supplier =>
                    supplier.name.toLowerCase().includes(searchTerm) ||
                    supplier.contact.email.toLowerCase().includes(searchTerm) ||
                    (supplier.shopifyVendorNames && supplier.shopifyVendorNames.some(name =>
                        name.toLowerCase().includes(searchTerm)
                    )) ||
                    (supplier.tags && supplier.tags.some(tag =>
                        tag.toLowerCase().includes(searchTerm)
                    ))
                );

                // Temporarily update suppliers for rendering
                const originalSuppliers = suppliers;
                suppliers = filteredSuppliers;
                renderSuppliersTable();
                suppliers = originalSuppliers;
            }, 300); // 300ms debounce delay
        }

        // Open add modal
        function openAddModal() {
            editingSupplier = null;
            document.getElementById('modal-title').textContent = 'Add New Supplier';
            document.getElementById('supplier-form').reset();
            document.getElementById('supplier-modal').style.display = 'block';
        }

        // Edit supplier
        async function editSupplier(supplierId) {
            try {
                const response = await fetch(`${API_BASE}/suppliers/${supplierId}`);
                const data = await response.json();
                
                if (data.success) {
                    editingSupplier = data.data;
                    document.getElementById('modal-title').textContent = 'Edit Supplier';
                    
                    // Populate form
                    document.getElementById('name').value = editingSupplier.name;
                    document.getElementById('priority').value = editingSupplier.priority;
                    document.getElementById('verified').value = editingSupplier.verified.toString();
                    document.getElementById('email').value = editingSupplier.contact.email;
                    document.getElementById('phone').value = editingSupplier.contact.phone || '';
                    document.getElementById('website').value = editingSupplier.contact.website || '';
                    document.getElementById('vendor-names').value = editingSupplier.shopifyVendorNames ? editingSupplier.shopifyVendorNames.join(', ') : '';
                    document.getElementById('tags').value = editingSupplier.tags ? editingSupplier.tags.join(', ') : '';
                    document.getElementById('processing-time').value = editingSupplier.automation.processingTime;
                    document.getElementById('requires-approval').value = editingSupplier.automation.requiresApproval.toString();
                    document.getElementById('notes').value = editingSupplier.notes || '';
                    
                    document.getElementById('supplier-modal').style.display = 'block';
                } else {
                    showError('Failed to load supplier: ' + data.error);
                }
            } catch (error) {
                showError('Failed to load supplier: ' + error.message);
            }
        }

        // Delete supplier
        async function deleteSupplier(supplierId) {
            if (!confirm('Are you sure you want to delete this supplier?')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/suppliers/${supplierId}`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                
                if (data.success) {
                    showSuccess('Supplier deleted successfully');
                    loadSuppliers();
                    loadStats();
                } else {
                    showError('Failed to delete supplier: ' + data.error);
                }
            } catch (error) {
                showError('Failed to delete supplier: ' + error.message);
            }
        }

        // Close modal
        function closeModal() {
            document.getElementById('supplier-modal').style.display = 'none';
            editingSupplier = null;
        }

        // Handle form submission
        document.getElementById('supplier-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const supplierData = {
                name: formData.get('name'),
                priority: formData.get('priority'),
                verified: formData.get('verified') === 'true',
                contact: {
                    email: formData.get('email'),
                    phone: formData.get('phone'),
                    website: formData.get('website')
                },
                shopifyVendorNames: formData.get('vendorNames') ? formData.get('vendorNames').split(',').map(s => s.trim()) : [],
                tags: formData.get('tags') ? formData.get('tags').split(',').map(s => s.trim()) : [],
                automation: {
                    processingTime: formData.get('processingTime'),
                    requiresApproval: formData.get('requiresApproval') === 'true',
                    autoRefund: false
                },
                notes: formData.get('notes')
            };

            try {
                const url = editingSupplier ? `${API_BASE}/suppliers/${editingSupplier._id}` : `${API_BASE}/suppliers`;
                const method = editingSupplier ? 'PUT' : 'POST';
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(supplierData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showSuccess(editingSupplier ? 'Supplier updated successfully' : 'Supplier created successfully');
                    closeModal();
                    loadSuppliers();
                    loadStats();
                } else {
                    showError('Failed to save supplier: ' + data.error);
                }
            } catch (error) {
                showError('Failed to save supplier: ' + error.message);
            }
        });

        // Test API connection
        async function testApiConnection() {
            console.log('🧪 Testing API connection...');

            try {
                // Test health endpoint
                console.log('Testing /health...');
                const healthResponse = await fetch('/health');
                const healthData = await healthResponse.json();
                console.log('Health response:', healthData);

                // Test admin test endpoint
                console.log('Testing /admin/test...');
                const adminResponse = await fetch('/admin/test');
                const adminData = await adminResponse.json();
                console.log('Admin test response:', adminData);

                // Test suppliers endpoint
                console.log('Testing /admin/suppliers...');
                const suppliersResponse = await fetch('/admin/suppliers');
                const suppliersData = await suppliersResponse.json();
                console.log('Suppliers response:', suppliersData);

                // Test stats endpoint
                console.log('Testing /admin/stats/suppliers...');
                const statsResponse = await fetch('/admin/stats/suppliers');
                const statsData = await statsResponse.json();
                console.log('Stats response:', statsData);

                showSuccess(`API Test Results:
                    Health: ${healthResponse.status}
                    Admin: ${adminResponse.status}
                    Suppliers: ${suppliersResponse.status} (${suppliersData.count || 0} items)
                    Stats: ${statsResponse.status}
                    MongoDB: ${healthData.database?.status || 'unknown'}
                `);

            } catch (error) {
                console.error('API test failed:', error);
                showError('API test failed: ' + error.message);
            }
        }

        // Refresh data
        function refreshData() {
            loadSuppliers();
            loadStats();
            showSuccess('Data refreshed successfully');
        }

        // Show error message
        function showError(message) {
            // Remove existing messages
            const existing = document.querySelector('.error, .success');
            if (existing) existing.remove();
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            document.querySelector('.container').insertBefore(errorDiv, document.querySelector('.stats-grid'));
            
            setTimeout(() => errorDiv.remove(), 5000);
        }

        // Show success message
        function showSuccess(message) {
            // Remove existing messages
            const existing = document.querySelector('.error, .success');
            if (existing) existing.remove();
            
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            document.querySelector('.container').insertBefore(successDiv, document.querySelector('.stats-grid'));
            
            setTimeout(() => successDiv.remove(), 3000);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('supplier-modal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
